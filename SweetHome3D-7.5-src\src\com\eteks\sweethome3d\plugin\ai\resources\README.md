# AI Plugin Resources

This directory contains resources for the Sweet Home 3D AI Plugin.

## Required Icons

The following icon files should be placed in this directory:

### ai-icon-16.png
- Size: 16x16 pixels
- Format: PNG
- Usage: Toolbar button icon
- Description: Small AI icon for the toolbar

### ai-icon-24.png
- Size: 24x24 pixels
- Format: PNG
- Usage: Menu icon
- Description: Medium AI icon for menus

### ai-icon-32.png
- Size: 32x32 pixels
- Format: PNG
- Usage: Dialog icon
- Description: Large AI icon for dialogs

## Icon Design Guidelines

- Use a simple, recognizable AI symbol (e.g., brain, circuit, robot head)
- Ensure good contrast and visibility at small sizes
- Follow Sweet Home 3D's visual style
- Use consistent colors across all sizes

## Localization Files

Additional properties files for different languages can be added:

- ApplicationPlugin_fr.properties (French)
- ApplicationPlugin_de.properties (German)
- ApplicationPlugin_es.properties (Spanish)
- etc.

## Example Icon Creation

You can create simple icons using any image editor or online icon generators.
Recommended tools:
- GIMP (free)
- Inkscape (free, vector-based)
- Online icon generators
- AI-generated icons

## Installation

When packaging the plugin as a JAR file, ensure all resources are included
in the correct directory structure.

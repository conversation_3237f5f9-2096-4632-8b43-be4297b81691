/*
 * AIErrorHandler.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import javax.swing.JOptionPane;
import java.awt.Component;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * Handles AI-related errors and provides user-friendly error messages.
 * 
 * <AUTHOR> Integration Team
 */
public class AIErrorHandler {
  
  /**
   * Handles analysis errors and displays appropriate messages to the user.
   * 
   * @param error The error that occurred
   * @param parentComponent The parent component for the error dialog
   */
  public void handleAnalysisError(Throwable error, Component parentComponent) {
    String message;
    String title = "AI Analysis Error";
    
    if (error instanceof IllegalStateException) {
      message = "AI service is not configured. Please check your AI settings.";
    } else if (error.getCause() instanceof ConnectException || error instanceof ConnectException) {
      message = "Cannot connect to AI service. Please check your network connection and AI settings.";
    } else if (error.getCause() instanceof UnknownHostException || error instanceof UnknownHostException) {
      message = "Cannot resolve AI service hostname. Please check your network connection and AI settings.";
    } else if (error.getCause() instanceof SocketTimeoutException || error instanceof SocketTimeoutException) {
      message = "Request timed out. The AI service may be overloaded. Please try again later.";
    } else if (error.getMessage() != null) {
      String errorMsg = error.getMessage().toLowerCase();
      if (errorMsg.contains("401") || errorMsg.contains("unauthorized")) {
        message = "Authentication failed. Please check your API key in AI settings.";
      } else if (errorMsg.contains("403") || errorMsg.contains("forbidden")) {
        message = "Access denied. Please check your API key permissions in AI settings.";
      } else if (errorMsg.contains("429") || errorMsg.contains("rate limit")) {
        message = "Rate limit exceeded. Please try again later.";
      } else if (errorMsg.contains("404") || errorMsg.contains("not found")) {
        message = "AI service endpoint not found. Please check your base URL in AI settings.";
      } else if (errorMsg.contains("500") || errorMsg.contains("internal server error")) {
        message = "AI service is experiencing issues. Please try again later.";
      } else if (errorMsg.contains("502") || errorMsg.contains("bad gateway")) {
        message = "AI service is temporarily unavailable. Please try again later.";
      } else if (errorMsg.contains("503") || errorMsg.contains("service unavailable")) {
        message = "AI service is temporarily unavailable. Please try again later.";
      } else {
        message = "An unexpected error occurred: " + error.getMessage();
      }
    } else {
      message = "An unexpected error occurred. Please check your AI settings and try again.";
    }
    
    JOptionPane.showMessageDialog(parentComponent, message, title, JOptionPane.ERROR_MESSAGE);
  }
  
  /**
   * Handles configuration errors and displays appropriate messages to the user.
   * 
   * @param error The error that occurred
   * @param parentComponent The parent component for the error dialog
   */
  public void handleConfigurationError(Throwable error, Component parentComponent) {
    String message;
    String title = "AI Configuration Error";
    
    if (error instanceof IllegalArgumentException) {
      message = "Invalid configuration: " + error.getMessage();
    } else {
      message = "Configuration error: " + (error.getMessage() != null ? error.getMessage() : "Unknown error");
    }
    
    JOptionPane.showMessageDialog(parentComponent, message, title, JOptionPane.ERROR_MESSAGE);
  }
  
  /**
   * Handles connection test errors and returns a user-friendly message.
   * 
   * @param error The error that occurred during connection testing
   * @return A user-friendly error message
   */
  public String getConnectionTestErrorMessage(Throwable error) {
    if (error instanceof ConnectException) {
      return "Cannot connect to the AI service. Please check the base URL and your network connection.";
    } else if (error instanceof UnknownHostException) {
      return "Cannot resolve the AI service hostname. Please check the base URL.";
    } else if (error instanceof SocketTimeoutException) {
      return "Connection timed out. The AI service may be unavailable.";
    } else if (error.getMessage() != null) {
      String errorMsg = error.getMessage().toLowerCase();
      if (errorMsg.contains("401") || errorMsg.contains("unauthorized")) {
        return "Authentication failed. Please check your API key.";
      } else if (errorMsg.contains("403") || errorMsg.contains("forbidden")) {
        return "Access denied. Please check your API key permissions.";
      } else if (errorMsg.contains("404") || errorMsg.contains("not found")) {
        return "AI service endpoint not found. Please check the base URL.";
      } else {
        return "Connection failed: " + error.getMessage();
      }
    } else {
      return "Connection failed for an unknown reason.";
    }
  }
}

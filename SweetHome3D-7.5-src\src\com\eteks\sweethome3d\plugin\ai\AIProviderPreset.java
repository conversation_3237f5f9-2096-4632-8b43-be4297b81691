/*
 * AIProviderPreset.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Pre-configured settings for popular AI providers as of September 2025.
 * Includes major commercial providers, API aggregators, and local LLM tools.
 * 
 * <AUTHOR> Integration Team
 */
public enum AIProviderPreset {
  // Major Commercial Providers (September 2025)
  OPENAI("OpenAI", "https://api.openai.com/v1",
         Arrays.asList("gpt-5", "gpt-4.1", "o3", "o4-mini", "gpt-4o")),
  
  ANTHROPIC("Anthropic", "https://api.anthropic.com/v1",
            Arrays.asList("claude-opus-4.1", "claude-sonnet-4", "claude-3.5-sonnet")),
  
  GOOGLE("Google AI", "https://generativelanguage.googleapis.com/v1",
         Arrays.asList("gemini-2.5-pro", "gemini-2.0-flash", "gemini-pro")),
  
  XAI("xAI", "https://api.x.ai/v1",
      Arrays.asList("grok-4", "grok-3", "grok-2")),
  
  // API Aggregators & Platforms
  TOGETHER_AI("Together AI", "https://api.together.xyz/v1",
              Arrays.asList("meta-llama/Llama-4-Scout", "mistralai/Mistral-7B-Instruct", "deepseek-ai/deepseek-r1")),
  
  FIREWORKS("Fireworks AI", "https://api.fireworks.ai/inference/v1",
            Arrays.asList("accounts/fireworks/models/llama-v3p1-405b-instruct", "accounts/fireworks/models/mixtral-8x7b-instruct")),
  
  OPENROUTER("OpenRouter", "https://openrouter.ai/api/v1",
             Arrays.asList("anthropic/claude-3.5-sonnet", "openai/gpt-4", "meta-llama/llama-3.1-405b")),
  
  GROQ("Groq", "https://api.groq.com/openai/v1",
       Arrays.asList("llama-3.1-405b-reasoning", "llama-3.1-70b-versatile", "mixtral-8x7b-32768")),
  
  DEEPINFRA("DeepInfra", "https://api.deepinfra.com/v1/openai",
            Arrays.asList("meta-llama/Meta-Llama-3.1-405B-Instruct", "microsoft/WizardLM-2-8x22B")),
  
  // Local & Self-Hosted Solutions
  OLLAMA("Ollama (Local)", "http://localhost:11434/v1",
         Arrays.asList("llama3.3", "qwen2.5", "deepseek-r1", "phi3", "mistral")),
  
  LM_STUDIO("LM Studio (Local)", "http://localhost:1234/v1",
            Arrays.asList("llama-3.1-8b-instruct", "phi-3-mini-4k-instruct", "mistral-7b-instruct")),
  
  ANYTHINGLLM("AnythingLLM (Local)", "http://localhost:3001/api/v1",
              Arrays.asList("llama3", "mistral", "codellama")),
  
  JAN("Jan (Local)", "http://localhost:1337/v1",
      Arrays.asList("llama3-8b", "phi3-mini", "gemma-2b")),
  
  CUSTOM("Custom Provider", "", Collections.emptyList());
  
  private final String displayName;
  private final String baseUrl;
  private final List<String> defaultModels;
  
  AIProviderPreset(String displayName, String baseUrl, List<String> defaultModels) {
    this.displayName = displayName;
    this.baseUrl = baseUrl;
    this.defaultModels = defaultModels;
  }
  
  /**
   * Returns the display name of the provider.
   */
  public String getDisplayName() {
    return displayName;
  }
  
  /**
   * Returns the base URL for the provider's API.
   */
  public String getBaseUrl() {
    return baseUrl;
  }
  
  /**
   * Returns the list of default models for this provider.
   */
  public List<String> getDefaultModels() {
    return Collections.unmodifiableList(defaultModels);
  }
  
  /**
   * Creates an AIProviderConfig from this preset.
   */
  public AIProviderConfig createConfig() {
    return AIProviderConfig.builder()
        .providerName(displayName)
        .baseUrl(baseUrl)
        .model(defaultModels.isEmpty() ? "" : defaultModels.get(0))
        .build();
  }
  
  /**
   * Creates an AIProviderConfig from this preset with the specified API key.
   */
  public AIProviderConfig createConfig(String apiKey) {
    return AIProviderConfig.builder()
        .providerName(displayName)
        .baseUrl(baseUrl)
        .apiKey(apiKey)
        .model(defaultModels.isEmpty() ? "" : defaultModels.get(0))
        .build();
  }
  
  /**
   * Returns whether this provider requires an API key.
   */
  public boolean requiresApiKey() {
    // Local providers typically don't require API keys
    return this != OLLAMA && this != LM_STUDIO && this != ANYTHINGLLM && this != JAN && this != CUSTOM;
  }
  
  @Override
  public String toString() {
    return displayName;
  }
}

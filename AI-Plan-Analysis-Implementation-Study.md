# AI Plan Analysis Implementation Study

## 1. Introduction

This document outlines the plan for integrating an AI-powered analysis feature into the Sweet Home 3D application. The goal of this feature is to provide users with a comprehensive analysis of their floor plans, offering suggestions and improvements based on best practices in construction and design. When a user clicks on a new "AI" icon, a chat-box will open, and the AI will evaluate the floor plan data, including all elements and their properties, as well as the compass data.

## 2. Architecture Overview

The AI integration will be implemented as a new plugin, which will allow for a clean and modular separation from the core application. The plugin will consist of the following components:

*   **A new `Plugin` class**: This class will be the main entry point for the plugin and will be responsible for creating and managing the plugin's actions.
*   **A new `PluginAction`**: This action will be triggered when the user clicks the AI icon in the toolbar. It will be responsible for opening the AI chat-box and initiating the analysis.
*   **An `AIIntegration` class**: This class will handle the data extraction, the communication with the AI service, and the processing of the AI's response.
*   **A chat-box UI**: This will be a simple `JDialog` that displays the AI's analysis to the user.

## 3. Data Model

The data for the AI analysis will be extracted from the `Home` object, which is the central data model for the application. The following data will be extracted for each object in the home:

*   **`Home`**: The overall properties of the home, such as its name, wall height, and environment settings.
*   **`Level`**: For each level, its elevation, height, and floor thickness.
*   **`Wall`**: For each wall, its start and end points, thickness, height, and the colors and textures of its sides.
*   **`Room`**: For each room, its points, area, and the colors and textures of its floor and ceiling.
*   **`PieceOfFurniture`**: For each piece of furniture, its name, description, dimensions, elevation, and any custom properties.
*   **`Compass`**: The compass's latitude, longitude, and north direction.

This data will be serialized into a JSON object, which will be sent to the AI service for analysis.

## 4. UI Design

The AI chat-box will be a simple `JDialog` with the following components:

*   **A `JTextArea` or `JTextPane`**: This will display the AI's analysis to the user.
*   **A "Close" button**: This will allow the user to dismiss the dialog.

The chat-box will be non-modal, allowing the user to continue to interact with the main application while the AI analysis is in progress.

## 5. AI Integration Strategy

The AI integration will be implemented using the following strategy:

*   **AI Service**: Support for multiple OpenAI-compatible LLM providers (as of September 2025):
    - **Major Commercial Providers**: OpenAI (GPT-5, GPT-4.1, o3), Anthropic (Claude Opus 4.1), Google (Gemini 2.5 Pro), xAI (Grok 4), Meta (Llama 4 Scout), Alibaba (Qwen 3 Max), DeepSeek (DeepSeek R1), Mistral AI (Mistral Large 2), Amazon (Nova Premier)
    - **API Aggregators**: Together AI, Fireworks AI, OpenRouter, Groq, Hyperbolic, DeepInfra, Replicate, Anyscale, Perplexity AI
    - **Local LLM Tools**: Ollama, LM Studio, AnythingLLM, GPT4All, Jan, text-generation-webui, LocalAI
*   **API Integration**: The implementation will use the **Chat Completions API** (`/v1/chat/completions`) endpoint, which is the current standard for modern AI models. The legacy Completions API (`/v1/completions`) is deprecated and not supported by modern models.
*   **OpenAI Java SDK**: The official OpenAI Java SDK will be used to handle API communication, providing robust support for chat completions with proper message formatting (system, user, assistant roles).
*   **Provider Configuration**: A flexible configuration system will allow users to choose their preferred AI provider, including self-hosted solutions.
*   **Data Serialization**: The `AIIntegration` class will be responsible for serializing the `Home` object into a JSON string.
*   **Asynchronous Execution**: The API call will be executed in a separate thread to avoid blocking the main application thread.
*   **Error Handling**: Robust error handling will be implemented to manage any issues that may arise during the API call.

## 6. Implementation Steps

The implementation of the AI feature will be carried out in the following steps:

1.  **Create the plugin directory**: A new directory will be created at `SweetHome3D-7.5-src/src/com/eteks/sweethome3d/plugin/ai`.
2.  **Create the `AIPlugin` class**: This class will extend the `Plugin` class and will be responsible for creating the `AIAction`.
3.  **Create the `AIAction` class**: This class will extend the `PluginAction` class and will be responsible for opening the chat-box and initiating the AI analysis.
4.  **Create the `AIIntegration` class**: This class will contain the logic for extracting the floor plan data, serializing it to JSON, and sending it to the AI service using the Chat Completions API.
5.  **Create the chat-box UI**: A new `JDialog` will be created to display the AI's analysis.
6.  **Add the AI icon to the toolbar**: The `AIAction` will be added to the main toolbar in the `HomePane` class.

### 6.1 Technical Implementation Notes

**API Endpoint Usage:**
- **Use**: `client.chat().completions().create(params)` with `ChatCompletionCreateParams`
- **Avoid**: The deprecated `/v1/completions` endpoint which is not supported by modern models
- **Message Format**: Structure prompts using the chat format with system and user messages:
  ```java
  ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
      .model(config.getModel())  // Configurable model (e.g., "gpt-5", "claude-opus-4.1", "llama-4-scout")
      .addSystemMessage("You are an expert architect and interior designer...")
      .addUserMessage("Please analyze this floor plan: " + floorPlanData)
      .build();
  ```

**Provider Configuration:**
- **Configuration System**: Users can configure their preferred AI provider through a settings dialog
- **Self-Hosted Support**: Full support for local LLM deployments (Ollama, LM Studio, etc.) with custom base URLs
- **No Organization/Project Settings**: These are not needed for self-hosting and have been removed for simplicity

## 7. Testing

The new AI feature will be thoroughly tested to ensure that it's working correctly and that it meets all the requirements. The testing will include the following:

*   **Unit tests**: Unit tests will be written for the `AIIntegration` class to ensure that the data extraction and serialization are working correctly.
*   **Integration tests**: Integration tests will be written to test the entire workflow, from clicking the AI icon to displaying the AI's analysis in the chat-box.
*   **User acceptance testing**: The feature will be tested by a group of users to get their feedback and to ensure that it's easy to use and provides valuable insights.

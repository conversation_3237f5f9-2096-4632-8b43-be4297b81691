# AI Integration Plan for Sweet Home 3D

## 1. Overview

This document outlines the comprehensive plan for integrating AI-powered analysis capabilities into Sweet Home 3D. The integration will support multiple OpenAI-compatible providers and models, allowing users to configure their preferred AI service for floor plan analysis.

**⚠️ Important API Endpoint Note**: This implementation uses the **Chat Completions API** (`/v1/chat/completions`) which is the current standard for modern AI models. The legacy Completions API (`/v1/completions`) is deprecated and not supported by modern models like GPT-4, GPT-4o, or GPT-3.5-turbo.

## 2. AI Provider Configuration System

### 2.1 Supported Provider Types

The system will support any OpenAI-compatible API provider (as of September 2025):

**Major Commercial Providers:**
- **OpenAI** (GPT-5, GPT-4.1, o3, o4-mini)
- **Anthropic** (Claude Opus 4.1, Claude <PERSON> 4)
- **Google** (Gemini 2.5 Pro, Gemini 2.0 Flash)
- **xAI** (Grok 4, Grok 3)
- **Meta** (Llama 4 Scout)
- **Alibaba** (Qwen 3 Max, Qwen3-235B)
- **DeepSeek** (DeepSeek R1, DeepSeek-V3)
- **Mistral AI** (Mistral Large 2, Mistral Medium 3)
- **Amazon** (Nova Premier, Nova Pro, Nova Micro)

**API Aggregators & Platforms:**
- **Together AI** (200+ models, 11x more affordable than GPT-4)
- **Fireworks AI** (Fast inference, optimized models)
- **OpenRouter** (Multi-provider routing)
- **Groq** (Ultra-fast inference, 275 tokens/sec)
- **Hyperbolic** (Cost-effective inference)
- **DeepInfra** (Competitive pricing)
- **Replicate** (Easy model deployment)
- **Anyscale** (Ray-based scaling)
- **Perplexity AI** (Search-augmented models)

**Local & Self-Hosted Solutions:**
- **Ollama** (Most popular local LLM tool)
- **LM Studio** (GUI-based local deployment)
- **AnythingLLM** (Privacy-focused local AI)
- **GPT4All** (Cross-platform local models)
- **Jan** (100% offline ChatGPT alternative)
- **text-generation-webui** (Flexible web interface)
- **LocalAI** (OpenAI API drop-in replacement)
- **Custom endpoints** (self-hosted models)

### 2.2 Configuration Data Model

```java
public class AIProviderConfig {
    private String providerName;        // Display name (e.g., "OpenAI", "Anthropic", "Together AI")
    private String baseUrl;             // API endpoint URL
    private String apiKey;              // Authentication key
    private String model;               // Model identifier (e.g., "gpt-5", "claude-opus-4.1", "llama-4-scout")
    private Map<String, String> customHeaders; // Optional: custom headers for self-hosted providers
    private AIModelParameters modelParams;     // Model-specific parameters

    // Builder pattern implementation
    public static class Builder {
        // Builder methods for flexible configuration
    }
}

public class AIModelParameters {
    private double temperature = 0.7;
    private int maxTokens = 2048;
    private double topP = 1.0;
    private int frequencyPenalty = 0;
    private int presencePenalty = 0;
    // Additional model parameters as needed
}
```

### 2.3 Configuration Storage

Configuration will be stored using Sweet Home 3D's existing preferences system:

```java
public class AIConfigurationManager {
    private static final String AI_PROVIDER_NAME = "aiProviderName";
    private static final String AI_BASE_URL = "aiBaseUrl";
    private static final String AI_API_KEY = "aiApiKey";
    private static final String AI_MODEL = "aiModel";
    private static final String AI_CUSTOM_HEADERS = "aiCustomHeaders";
    // Additional preference keys

    public void saveConfiguration(AIProviderConfig config);
    public AIProviderConfig loadConfiguration();
    public List<AIProviderConfig> getPresetConfigurations();
}
```

## 3. Configuration UI Components

### 3.1 AI Settings Dialog

A dedicated settings dialog accessible from the main menu:

```
Menu: Tools > AI Settings...
```

**Dialog Components:**
- Provider selection dropdown (with presets + custom option)
- Base URL field (auto-populated for presets, editable for custom)
- API Key field (password-masked)
- Model selection dropdown (populated based on provider)
- Advanced settings panel (collapsible)
- Test connection button
- Save/Cancel buttons

### 3.2 Provider Presets

Pre-configured settings for common providers:

```java
public enum AIProviderPreset {
    // Major Commercial Providers (September 2025)
    OPENAI("OpenAI", "https://api.openai.com/v1",
           Arrays.asList("gpt-5", "gpt-4.1", "o3", "o4-mini", "gpt-4o")),
    ANTHROPIC("Anthropic", "https://api.anthropic.com/v1",
              Arrays.asList("claude-opus-4.1", "claude-sonnet-4", "claude-3.5-sonnet")),
    GOOGLE("Google AI", "https://generativelanguage.googleapis.com/v1",
           Arrays.asList("gemini-2.5-pro", "gemini-2.0-flash", "gemini-pro")),
    XAI("xAI", "https://api.x.ai/v1",
        Arrays.asList("grok-4", "grok-3", "grok-2")),

    // API Aggregators & Platforms
    TOGETHER_AI("Together AI", "https://api.together.xyz/v1",
                Arrays.asList("meta-llama/Llama-4-Scout", "mistralai/Mistral-7B-Instruct", "deepseek-ai/deepseek-r1")),
    FIREWORKS("Fireworks AI", "https://api.fireworks.ai/inference/v1",
              Arrays.asList("accounts/fireworks/models/llama-v3p1-405b-instruct", "accounts/fireworks/models/mixtral-8x7b-instruct")),
    OPENROUTER("OpenRouter", "https://openrouter.ai/api/v1",
               Arrays.asList("anthropic/claude-3.5-sonnet", "openai/gpt-4", "meta-llama/llama-3.1-405b")),
    GROQ("Groq", "https://api.groq.com/openai/v1",
         Arrays.asList("llama-3.1-405b-reasoning", "llama-3.1-70b-versatile", "mixtral-8x7b-32768")),
    DEEPINFRA("DeepInfra", "https://api.deepinfra.com/v1/openai",
              Arrays.asList("meta-llama/Meta-Llama-3.1-405B-Instruct", "microsoft/WizardLM-2-8x22B")),

    // Local & Self-Hosted Solutions
    OLLAMA("Ollama (Local)", "http://localhost:11434/v1",
           Arrays.asList("llama3.3", "qwen2.5", "deepseek-r1", "phi3", "mistral")),
    LM_STUDIO("LM Studio (Local)", "http://localhost:1234/v1",
              Arrays.asList("llama-3.1-8b-instruct", "phi-3-mini-4k-instruct", "mistral-7b-instruct")),
    ANYTHINGLLM("AnythingLLM (Local)", "http://localhost:3001/api/v1",
                Arrays.asList("llama3", "mistral", "codellama")),
    JAN("Jan (Local)", "http://localhost:1337/v1",
        Arrays.asList("llama3-8b", "phi3-mini", "gemma-2b")),

    CUSTOM("Custom Provider", "", Collections.emptyList());
}
```

### 3.3 Model Discovery

Automatic model discovery for supported providers:

```java
public class AIModelDiscovery {
    public List<String> discoverAvailableModels(AIProviderConfig config);
    public boolean testConnection(AIProviderConfig config);
    public ModelCapabilities getModelCapabilities(String model);
}
```

## 4. AI Client Implementation

### 4.1 Unified AI Client Interface

**Important**: This implementation uses the **Chat Completions API** (`/v1/chat/completions`) which is the current standard for modern AI models. The legacy Completions API (`/v1/completions`) is deprecated and not supported by models like GPT-4, GPT-4o, or GPT-3.5-turbo.

```java
public interface AIClient {
    CompletableFuture<String> analyzeFloorPlan(String floorPlanData, String prompt);
    boolean testConnection();
    List<String> getAvailableModels();
    void close();
}

public class OpenAICompatibleClient implements AIClient {
    private final OpenAIClient client;
    private final AIProviderConfig config;

    public OpenAICompatibleClient(AIProviderConfig config) {
        this.config = config;
        OpenAIOkHttpClient.Builder clientBuilder = OpenAIOkHttpClient.builder()
            .baseUrl(config.getBaseUrl())
            .apiKey(config.getApiKey());

        // Add custom headers for self-hosted providers if specified
        if (config.getCustomHeaders() != null && !config.getCustomHeaders().isEmpty()) {
            config.getCustomHeaders().forEach(clientBuilder::addHeader);
        }

        this.client = clientBuilder.build();
    }

    @Override
    public CompletableFuture<String> analyzeFloorPlan(String floorPlanData, String prompt) {
        // Use Chat Completions API (/v1/chat/completions) - the current standard
        // NOT the deprecated Completions API (/v1/completions)
        ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
            .model(config.getModel())
            .temperature(config.getModelParams().getTemperature())
            .maxTokens(config.getModelParams().getMaxTokens())
            .addSystemMessage("You are an expert architect and interior designer...")
            .addUserMessage(prompt + "\n\nFloor plan data:\n" + floorPlanData)
            .build();

        return CompletableFuture.supplyAsync(() -> {
            try {
                // This calls /v1/chat/completions endpoint
                ChatCompletion completion = client.chat().completions().create(params);
                return completion.choices().get(0).message().content().orElse("");
            } catch (Exception e) {
                throw new RuntimeException("AI analysis failed", e);
            }
        });
    }
}
```

### 4.2 Client Factory

```java
public class AIClientFactory {
    public static AIClient createClient(AIProviderConfig config) {
        validateConfig(config);
        return new OpenAICompatibleClient(config);
    }

    private static void validateConfig(AIProviderConfig config) {
        if (config.getBaseUrl() == null || config.getBaseUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL is required");
        }
        if (config.getApiKey() == null || config.getApiKey().trim().isEmpty()) {
            throw new IllegalArgumentException("API Key is required");
        }
        if (config.getModel() == null || config.getModel().trim().isEmpty()) {
            throw new IllegalArgumentException("Model is required");
        }
    }
}
```

## 5. Integration with Existing Architecture

### 5.1 Updated AIIntegration Class

```java
public class AIIntegration {
    private final AIConfigurationManager configManager;
    private AIClient aiClient;

    public AIIntegration() {
        this.configManager = new AIConfigurationManager();
        initializeClient();
    }

    private void initializeClient() {
        AIProviderConfig config = configManager.loadConfiguration();
        if (config != null && isConfigurationValid(config)) {
            this.aiClient = AIClientFactory.createClient(config);
        }
    }

    public CompletableFuture<String> analyzeHome(Home home) {
        if (aiClient == null) {
            throw new IllegalStateException("AI client not configured. Please configure AI settings first.");
        }

        String floorPlanData = extractFloorPlanData(home);
        String analysisPrompt = buildAnalysisPrompt();

        return aiClient.analyzeFloorPlan(floorPlanData, analysisPrompt);
    }

    public boolean isConfigured() {
        return aiClient != null;
    }

    public void reconfigure() {
        if (aiClient != null) {
            aiClient.close();
        }
        initializeClient();
    }
}
```

### 5.2 Enhanced AI Action

```java
public class AIAction extends PluginAction {
    private final AIIntegration aiIntegration;

    @Override
    public void execute() {
        if (!aiIntegration.isConfigured()) {
            showConfigurationDialog();
            return;
        }

        // Proceed with existing analysis logic
        openAIChatBox();
    }

    private void showConfigurationDialog() {
        AISettingsDialog dialog = new AISettingsDialog(getHomeController());
        dialog.setVisible(true);

        if (dialog.isConfigurationSaved()) {
            aiIntegration.reconfigure();
            // Retry analysis
            execute();
        }
    }
}
```

## 6. Error Handling and Validation

### 6.1 Configuration Validation

```java
public class AIConfigValidator {
    public ValidationResult validate(AIProviderConfig config) {
        List<String> errors = new ArrayList<>();

        if (isBlank(config.getBaseUrl())) {
            errors.add("Base URL is required");
        } else if (!isValidUrl(config.getBaseUrl())) {
            errors.add("Base URL must be a valid URL");
        }

        if (isBlank(config.getApiKey())) {
            errors.add("API Key is required");
        }

        if (isBlank(config.getModel())) {
            errors.add("Model selection is required");
        }

        return new ValidationResult(errors.isEmpty(), errors);
    }

    public CompletableFuture<ConnectionTestResult> testConnection(AIProviderConfig config) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                AIClient testClient = AIClientFactory.createClient(config);
                boolean connected = testClient.testConnection();
                testClient.close();

                return new ConnectionTestResult(connected,
                    connected ? "Connection successful" : "Connection failed");
            } catch (Exception e) {
                return new ConnectionTestResult(false, "Error: " + e.getMessage());
            }
        });
    }
}
```

### 6.2 Runtime Error Handling

```java
public class AIErrorHandler {
    public void handleAnalysisError(Throwable error, Component parentComponent) {
        String message;
        String title = "AI Analysis Error";

        if (error instanceof IllegalStateException) {
            message = "AI service is not configured. Please check your AI settings.";
        } else if (error.getCause() instanceof ConnectException) {
            message = "Cannot connect to AI service. Please check your network connection and AI settings.";
        } else if (error.getMessage().contains("401") || error.getMessage().contains("403")) {
            message = "Authentication failed. Please check your API key in AI settings.";
        } else if (error.getMessage().contains("429")) {
            message = "Rate limit exceeded. Please try again later.";
        } else {
            message = "An unexpected error occurred: " + error.getMessage();
        }

        JOptionPane.showMessageDialog(parentComponent, message, title, JOptionPane.ERROR_MESSAGE);
    }
}

## 7. UI Implementation Details

### 7.1 AI Settings Dialog Layout

```java
public class AISettingsDialog extends JDialog {
    private JComboBox<AIProviderPreset> providerComboBox;
    private JTextField baseUrlField;
    private JPasswordField apiKeyField;
    private JComboBox<String> modelComboBox;
    private JSlider temperatureSlider;
    private JSpinner maxTokensSpinner;
    private JButton testConnectionButton;
    private JButton discoverModelsButton;
    private JProgressBar connectionProgressBar;

    private void initializeComponents() {
        // Provider selection
        providerComboBox = new JComboBox<>(AIProviderPreset.values());
        providerComboBox.addActionListener(this::onProviderChanged);

        // Connection fields
        baseUrlField = new JTextField(30);
        apiKeyField = new JPasswordField(30);
        modelComboBox = new JComboBox<>();

        // Model parameters
        temperatureSlider = new JSlider(0, 200, 70); // 0.0 to 2.0, default 0.7
        temperatureSlider.setMajorTickSpacing(50);
        temperatureSlider.setPaintTicks(true);
        temperatureSlider.setPaintLabels(true);

        maxTokensSpinner = new JSpinner(new SpinnerNumberModel(2048, 1, 8192, 1));

        // Action buttons
        testConnectionButton = new JButton("Test Connection");
        testConnectionButton.addActionListener(this::testConnection);

        discoverModelsButton = new JButton("Discover Models");
        discoverModelsButton.addActionListener(this::discoverModels);
    }

    private void onProviderChanged(ActionEvent e) {
        AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
        if (preset != AIProviderPreset.CUSTOM) {
            baseUrlField.setText(preset.getBaseUrl());
            updateModelList(preset.getDefaultModels());
        } else {
            baseUrlField.setText("");
            modelComboBox.removeAllItems();
        }
    }
}
```

### 7.2 Enhanced AI Chat Dialog

```java
public class AIChatDialog extends JDialog {
    private JTextPane chatArea;
    private JTextField inputField;
    private JButton sendButton;
    private JButton newAnalysisButton;
    private JButton settingsButton;
    private JProgressBar analysisProgressBar;
    private final AIIntegration aiIntegration;
    private final Home home;

    public AIChatDialog(HomeController controller, AIIntegration aiIntegration) {
        super(SwingUtilities.getWindowAncestor(controller.getView()), "AI Floor Plan Analysis", false);
        this.aiIntegration = aiIntegration;
        this.home = controller.getHome();
        initializeComponents();
        startInitialAnalysis();
    }

    private void startInitialAnalysis() {
        if (!aiIntegration.isConfigured()) {
            showConfigurationPrompt();
            return;
        }

        analysisProgressBar.setVisible(true);
        sendButton.setEnabled(false);

        aiIntegration.analyzeHome(home)
            .thenAccept(this::displayAnalysis)
            .exceptionally(this::handleAnalysisError);
    }

    private void displayAnalysis(String analysis) {
        SwingUtilities.invokeLater(() -> {
            appendToChat("AI Assistant", analysis);
            analysisProgressBar.setVisible(false);
            sendButton.setEnabled(true);
        });
    }

    private Void handleAnalysisError(Throwable error) {
        SwingUtilities.invokeLater(() -> {
            new AIErrorHandler().handleAnalysisError(error, this);
            analysisProgressBar.setVisible(false);
            sendButton.setEnabled(true);
        });
        return null;
    }
}
```

## 8. Security Considerations

### 8.1 API Key Protection

```java
public class SecureConfigStorage {
    private static final String KEYSTORE_ALIAS = "sweethome3d_ai_config";

    public void storeApiKey(String apiKey) {
        try {
            // Use Java Keystore or OS credential store
            char[] keyChars = apiKey.toCharArray();
            // Store encrypted API key
            Arrays.fill(keyChars, '\0'); // Clear from memory
        } catch (Exception e) {
            // Fallback to obfuscated storage in preferences
            storeObfuscatedApiKey(apiKey);
        }
    }

    public String retrieveApiKey() {
        try {
            // Retrieve from secure storage
            return retrieveFromKeystore();
        } catch (Exception e) {
            // Fallback to deobfuscated retrieval
            return retrieveObfuscatedApiKey();
        }
    }

    private void storeObfuscatedApiKey(String apiKey) {
        // Simple obfuscation (not encryption) for basic protection
        byte[] obfuscated = obfuscate(apiKey.getBytes());
        String encoded = Base64.getEncoder().encodeToString(obfuscated);
        // Store in preferences
    }
}
```

### 8.2 Data Privacy

```java
public class PrivacyManager {
    public String sanitizeFloorPlanData(String floorPlanData, boolean includePersonalInfo) {
        if (!includePersonalInfo) {
            // Remove or anonymize personal information
            floorPlanData = floorPlanData.replaceAll("\"name\"\\s*:\\s*\"[^\"]*\"", "\"name\":\"Room\"");
            floorPlanData = removeCustomProperties(floorPlanData);
        }
        return floorPlanData;
    }

    public void showPrivacyNotice(Component parent) {
        String message = "This feature will send your floor plan data to the configured AI service.\n" +
                        "Please ensure you trust the AI provider with your data.\n" +
                        "Personal information can be excluded from the analysis.";

        JCheckBox excludePersonalInfo = new JCheckBox("Exclude personal information", true);
        Object[] components = {message, excludePersonalInfo};

        int result = JOptionPane.showConfirmDialog(parent, components,
            "Privacy Notice", JOptionPane.OK_CANCEL_OPTION);
    }
}
```

## 9. Configuration Migration and Compatibility

### 9.1 Version Compatibility

```java
public class ConfigurationMigrator {
    private static final int CURRENT_CONFIG_VERSION = 1;

    public AIProviderConfig migrateConfiguration(Properties oldConfig) {
        int version = Integer.parseInt(oldConfig.getProperty("configVersion", "0"));

        switch (version) {
            case 0:
                return migrateFromV0(oldConfig);
            case 1:
                return loadV1Configuration(oldConfig);
            default:
                throw new IllegalArgumentException("Unsupported configuration version: " + version);
        }
    }

    private AIProviderConfig migrateFromV0(Properties oldConfig) {
        // Handle migration from initial version without provider configuration
        return AIProviderConfig.builder()
            .providerName("OpenAI")
            .baseUrl("https://api.openai.com/v1")
            .apiKey(oldConfig.getProperty("aiApiKey", ""))
            .model(oldConfig.getProperty("aiModel", "gpt-4o"))  // Updated default model
            .build();
    }
}
```

### 9.2 Backward Compatibility

```java
public class LegacyAISupport {
    public boolean hasLegacyConfiguration() {
        // Check for old configuration format
        return preferences.get("aiApiKey", null) != null;
    }

    public void migrateLegacyConfiguration() {
        if (hasLegacyConfiguration()) {
            String apiKey = preferences.get("aiApiKey", "");
            String model = preferences.get("aiModel", "gpt-3.5-turbo");

            AIProviderConfig config = AIProviderConfig.builder()
                .providerName("OpenAI")
                .baseUrl("https://api.openai.com/v1")
                .apiKey(apiKey)
                .model(model.isEmpty() ? "gpt-4o" : model)  // Updated default model
                .build();

            new AIConfigurationManager().saveConfiguration(config);

            // Remove legacy keys
            preferences.remove("aiApiKey");
            preferences.remove("aiModel");
        }
    }
}
```

## 10. Testing Strategy

### 10.1 Unit Tests

```java
@Test
public class AIConfigurationTest {
    @Test
    public void testConfigurationValidation() {
        AIProviderConfig config = AIProviderConfig.builder()
            .providerName("Test Provider")
            .baseUrl("https://api.example.com/v1")
            .apiKey("test-key")
            .model("test-model")
            .build();

        AIConfigValidator validator = new AIConfigValidator();
        ValidationResult result = validator.validate(config);

        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    public void testInvalidConfiguration() {
        AIProviderConfig config = AIProviderConfig.builder()
            .providerName("Test Provider")
            .baseUrl("") // Invalid empty URL
            .apiKey("test-key")
            .model("test-model")
            .build();

        AIConfigValidator validator = new AIConfigValidator();
        ValidationResult result = validator.validate(config);

        assertFalse(result.isValid());
        assertFalse(result.getErrors().isEmpty());
    }
}
```

### 10.2 Integration Tests

```java
@Test
public class AIIntegrationTest {
    @Test
    public void testOpenAICompatibleClient() {
        // Mock OpenAI-compatible service
        MockWebServer mockServer = new MockWebServer();
        mockServer.enqueue(new MockResponse()
            .setBody("{\"choices\":[{\"message\":{\"content\":\"Test analysis\"}}]}")
            .addHeader("Content-Type", "application/json"));

        AIProviderConfig config = AIProviderConfig.builder()
            .baseUrl(mockServer.url("/").toString())
            .apiKey("test-key")
            .model("test-model")
            .build();

        AIClient client = AIClientFactory.createClient(config);
        CompletableFuture<String> result = client.analyzeFloorPlan("{}", "Test prompt");

        assertEquals("Test analysis", result.get());
        mockServer.shutdown();
    }
}
```

## 11. Documentation and User Guide

### 11.1 User Documentation

**AI Settings Configuration Guide:**

1. **Accessing AI Settings:**
   - Go to `Tools > AI Settings...`
   - Or click the gear icon in the AI chat dialog

2. **Configuring OpenAI:**
   - Select "OpenAI" from provider dropdown
   - Enter your OpenAI API key
   - Choose your preferred model
   - Click "Test Connection" to verify

3. **Configuring Azure OpenAI:**
   - Select "Azure OpenAI" from provider dropdown
   - Enter your Azure OpenAI endpoint URL
   - Enter your API key
   - Select your deployed model
   - Click "Test Connection" to verify

4. **Configuring Local LLM Tools:**

   **Ollama:**
   - Install and run Ollama locally
   - Select "Ollama (Local)" from provider dropdown
   - Verify the URL (default: http://localhost:11434/v1)
   - No API key required for local deployment
   - Select an available model (llama3.3, qwen2.5, deepseek-r1, etc.)
   - Click "Test Connection" to verify

   **LM Studio:**
   - Install and run LM Studio
   - Select "LM Studio (Local)" from provider dropdown
   - Verify the URL (default: http://localhost:1234/v1)
   - No API key required
   - Select from downloaded models

   **AnythingLLM:**
   - Install AnythingLLM desktop app
   - Select "AnythingLLM (Local)" from provider dropdown
   - Verify the URL (default: http://localhost:3001/api/v1)
   - Configure according to AnythingLLM setup

   **Jan:**
   - Install Jan desktop application
   - Select "Jan (Local)" from provider dropdown
   - Verify the URL (default: http://localhost:1337/v1)
   - 100% offline operation

### 11.2 Troubleshooting Guide

**Common Issues:**

1. **"AI service not configured" error:**
   - Solution: Configure AI settings via Tools > AI Settings

2. **"Connection failed" error:**
   - Check internet connection
   - Verify API key is correct
   - Ensure base URL is accessible

3. **"Authentication failed" error:**
   - Verify API key is valid and has sufficient permissions
   - Check if API key has expired

4. **"Rate limit exceeded" error:**
   - Wait before making another request
   - Consider upgrading your API plan

## 12. Implementation Timeline

### Phase 1: Core Configuration System (Week 1-2)
- Implement AIProviderConfig and related classes
- Create AIConfigurationManager
- Build basic validation system
- Unit tests for configuration components

### Phase 2: UI Components (Week 3-4)
- Implement AI Settings Dialog
- Create provider preset system
- Add connection testing functionality
- Integration with existing preferences system

### Phase 3: AI Client Integration (Week 5-6)
- Implement OpenAI-compatible client
- Create client factory and error handling
- Update existing AIIntegration class
- Add security measures for API key storage

### Phase 4: Enhanced Chat Interface (Week 7-8)
- Update AI chat dialog with new features
- Add configuration prompts and error handling
- Implement privacy controls
- User experience improvements

### Phase 5: Testing and Documentation (Week 9-10)
- Comprehensive testing (unit, integration, user acceptance)
- Documentation creation
- Performance optimization
- Final bug fixes and polish

## 13. Future Enhancements

### 13.1 Advanced Features
- **Model comparison:** Allow users to compare analyses from different models
- **Custom prompts:** Let users define custom analysis prompts
- **Analysis history:** Store and retrieve previous analyses
- **Batch processing:** Analyze multiple floor plans at once

### 13.2 Provider Extensions
- **Enhanced Local Tools:** Better integration with Ollama, LM Studio, AnythingLLM, Jan
- **Model Marketplace:** Integration with Hugging Face model hub
- **Performance Optimization:** Model quantization and optimization for local deployment
- **Multi-modal Support:** Image analysis capabilities for floor plans
- **Voice Integration:** Voice-to-text for natural language queries

This comprehensive plan ensures that the AI integration is flexible, secure, and user-friendly while supporting a wide range of OpenAI-compatible providers and models.

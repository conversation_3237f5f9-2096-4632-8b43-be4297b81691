/*
 * AIPlugin.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.plugin.Plugin;
import com.eteks.sweethome3d.plugin.PluginAction;

/**
 * Main plugin class for AI integration in Sweet Home 3D.
 * This plugin provides AI-powered floor plan analysis capabilities.
 * 
 * <AUTHOR> Integration Team
 */
public class AIPlugin extends Plugin {
  
  /**
   * Returns the actions provided by this plugin.
   */
  @Override
  public PluginAction[] getActions() {
    return new PluginAction[] {
      new AIAction(getHomeController())
    };
  }
  
  /**
   * Called when the plugin is destroyed.
   * Cleans up any resources used by the plugin.
   */
  @Override
  public void destroy() {
    super.destroy();
    // Any cleanup code would go here
  }
}

# ApplicationPlugin.properties
# Sweet Home 3D AI Plugin Configuration
# Copyright (c) 2025 AI Integration Team

# Plugin identification
id=ai-floor-plan-analysis
name=AI Floor Plan Analysis
description=Analyze floor plans using artificial intelligence to provide insights and improvement suggestions
version=1.0.0
license=GPL v2+
provider=AI Integration Team

# Plugin class
class=com.eteks.sweethome3d.plugin.ai.AIPlugin

# Java version requirement
javaMinimumVersion=1.8

# Plugin information
author=AI Integration Team
website=https://github.com/ai-integration-team/sweethome3d-ai-plugin
supportEmail=<EMAIL>

# Features
features=AI Analysis, Floor Plan Insights, Multiple AI Providers, Privacy Controls

# Requirements
requirements=Internet connection for cloud AI providers (optional for local providers)

# Compatibility
sweetHome3DMinimumVersion=7.0

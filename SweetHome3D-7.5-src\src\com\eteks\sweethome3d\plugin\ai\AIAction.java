/*
 * AIAction.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.plugin.PluginAction;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.SwingUtilities;

/**
 * Action that opens the AI analysis dialog when triggered.
 * This action is added to the Sweet Home 3D toolbar and menu.
 * 
 * <AUTHOR> Integration Team
 */
public class AIAction extends PluginAction {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  
  /**
   * Creates a new AI action.
   * 
   * @param homeController The home controller
   */
  public AIAction(HomeController homeController) {
    this.homeController = homeController;
    this.aiIntegration = new AIIntegration();
    
    // Set action properties
    putPropertyValue(Property.NAME, "AI Analysis");
    putPropertyValue(Property.SHORT_DESCRIPTION, "Analyze floor plan with AI");
    putPropertyValue(Property.TOOL_BAR, true);
    putPropertyValue(Property.MENU, "Tools");
    putPropertyValue(Property.ENABLED, true);

    // Set icon (placeholder - actual icon file should be added to resources)
    // putPropertyValue(Property.SMALL_ICON, new URLContent(getClass().getResource("resources/ai-icon-16.png")));
  }
  
  /**
   * Executes the AI action.
   */
  @Override
  public void execute() {
    // Check if AI is configured
    if (!aiIntegration.isConfigured()) {
      showConfigurationDialog();
      return;
    }
    
    // Open AI chat dialog
    openAIChatDialog();
  }
  
  /**
   * Shows the AI configuration dialog.
   */
  private void showConfigurationDialog() {
    SwingUtilities.invokeLater(() -> {
      AISettingsDialog dialog = new AISettingsDialog(homeController, aiIntegration);
      dialog.setVisible(true);
      
      // If configuration was saved, try to execute the action again
      if (dialog.isConfigurationSaved()) {
        aiIntegration.reconfigure();
        execute();
      }
    });
  }
  
  /**
   * Opens the AI chat dialog for analysis.
   */
  private void openAIChatDialog() {
    SwingUtilities.invokeLater(() -> {
      Home home = homeController.getHome();
      if (home == null) {
        return;
      }
      
      AIChatDialog chatDialog = new AIChatDialog(homeController, aiIntegration);
      chatDialog.setVisible(true);
    });
  }
  
  /**
   * Returns the home controller.
   */
  protected HomeController getHomeController() {
    return homeController;
  }
  
  /**
   * Returns the AI integration instance.
   */
  protected AIIntegration getAIIntegration() {
    return aiIntegration;
  }
}

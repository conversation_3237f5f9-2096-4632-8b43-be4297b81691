/*
 * FloorPlanDataExtractor.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.HomePieceOfFurniture;
import com.eteks.sweethome3d.model.Wall;
import com.eteks.sweethome3d.model.Room;
import com.eteks.sweethome3d.model.Level;
import com.eteks.sweethome3d.model.Compass;
import com.eteks.sweethome3d.model.HomeEnvironment;
import com.eteks.sweethome3d.model.HomeTexture;

/**
 * Extracts floor plan data from Sweet Home 3D Home objects and converts it to JSON.
 * This class handles the serialization of all relevant home data for AI analysis.
 * 
 * <AUTHOR> Integration Team
 */
public class FloorPlanDataExtractor {
  
  /**
   * Extracts floor plan data from a Home object and returns it as JSON.
   * 
   * @param home The home to extract data from
   * @return JSON representation of the floor plan data
   */
  public String extractToJson(Home home) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    
    // Home properties
    json.append("  \"home\": {\n");
    json.append("    \"name\": \"").append(escapeJson(home.getName())).append("\",\n");
    json.append("    \"wallHeight\": ").append(home.getWallHeight()).append(",\n");
    json.append("    \"modified\": ").append(home.isModified()).append("\n");
    json.append("  },\n");
    
    // Environment
    json.append("  \"environment\": ");
    json.append(extractEnvironment(home.getEnvironment()));
    json.append(",\n");
    
    // Compass
    json.append("  \"compass\": ");
    json.append(extractCompass(home.getCompass()));
    json.append(",\n");
    
    // Levels
    json.append("  \"levels\": [\n");
    boolean firstLevel = true;
    for (Level level : home.getLevels()) {
      if (!firstLevel) json.append(",\n");
      json.append("    ").append(extractLevel(level));
      firstLevel = false;
    }
    json.append("\n  ],\n");
    
    // Walls
    json.append("  \"walls\": [\n");
    boolean firstWall = true;
    for (Wall wall : home.getWalls()) {
      if (!firstWall) json.append(",\n");
      json.append("    ").append(extractWall(wall));
      firstWall = false;
    }
    json.append("\n  ],\n");
    
    // Rooms
    json.append("  \"rooms\": [\n");
    boolean firstRoom = true;
    for (Room room : home.getRooms()) {
      if (!firstRoom) json.append(",\n");
      json.append("    ").append(extractRoom(room));
      firstRoom = false;
    }
    json.append("\n  ],\n");
    
    // Furniture
    json.append("  \"furniture\": [\n");
    boolean firstFurniture = true;
    for (HomePieceOfFurniture piece : home.getFurniture()) {
      if (!firstFurniture) json.append(",\n");
      json.append("    ").append(extractFurniture(piece));
      firstFurniture = false;
    }
    json.append("\n  ]\n");
    
    json.append("}");
    return json.toString();
  }
  
  /**
   * Extracts environment data.
   */
  private String extractEnvironment(HomeEnvironment env) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("    \"groundColor\": \"#").append(Integer.toHexString(env.getGroundColor())).append("\",\n");
    json.append("    \"skyColor\": \"#").append(Integer.toHexString(env.getSkyColor())).append("\",\n");
    json.append("    \"lightColor\": \"#").append(Integer.toHexString(env.getLightColor())).append("\",\n");
    json.append("    \"wallsAlpha\": ").append(env.getWallsAlpha()).append(",\n");
    json.append("    \"allLevelsVisible\": ").append(env.isAllLevelsVisible()).append("\n");
    json.append("  }");
    return json.toString();
  }
  
  /**
   * Extracts compass data.
   */
  private String extractCompass(Compass compass) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("    \"x\": ").append(compass.getX()).append(",\n");
    json.append("    \"y\": ").append(compass.getY()).append(",\n");
    json.append("    \"diameter\": ").append(compass.getDiameter()).append(",\n");
    json.append("    \"northDirection\": ").append(compass.getNorthDirection()).append(",\n");
    json.append("    \"latitude\": ").append(compass.getLatitude()).append(",\n");
    json.append("    \"longitude\": ").append(compass.getLongitude()).append(",\n");
    json.append("    \"timeZone\": \"").append(escapeJson(compass.getTimeZone())).append("\",\n");
    json.append("    \"visible\": ").append(compass.isVisible()).append("\n");
    json.append("  }");
    return json.toString();
  }
  
  /**
   * Extracts level data.
   */
  private String extractLevel(Level level) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"name\": \"").append(escapeJson(level.getName())).append("\",\n");
    json.append("      \"elevation\": ").append(level.getElevation()).append(",\n");
    json.append("      \"height\": ").append(level.getHeight()).append(",\n");
    json.append("      \"floorThickness\": ").append(level.getFloorThickness()).append(",\n");
    json.append("      \"visible\": ").append(level.isVisible()).append(",\n");
    json.append("      \"viewable\": ").append(level.isViewable()).append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Extracts wall data.
   */
  private String extractWall(Wall wall) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"xStart\": ").append(wall.getXStart()).append(",\n");
    json.append("      \"yStart\": ").append(wall.getYStart()).append(",\n");
    json.append("      \"xEnd\": ").append(wall.getXEnd()).append(",\n");
    json.append("      \"yEnd\": ").append(wall.getYEnd()).append(",\n");
    json.append("      \"thickness\": ").append(wall.getThickness()).append(",\n");
    json.append("      \"height\": ").append(wall.getHeight() != null ? wall.getHeight() : "null").append(",\n");
    json.append("      \"heightAtEnd\": ").append(wall.getHeightAtEnd() != null ? wall.getHeightAtEnd() : "null").append(",\n");
    json.append("      \"arcExtent\": ").append(wall.getArcExtent() != null ? wall.getArcExtent() : "null").append(",\n");
    json.append("      \"leftSideColor\": ").append(wall.getLeftSideColor() != null ? "\"#" + Integer.toHexString(wall.getLeftSideColor()) + "\"" : "null").append(",\n");
    json.append("      \"rightSideColor\": ").append(wall.getRightSideColor() != null ? "\"#" + Integer.toHexString(wall.getRightSideColor()) + "\"" : "null").append(",\n");
    json.append("      \"level\": ").append(wall.getLevel() != null ? "\"" + escapeJson(wall.getLevel().getName()) + "\"" : "null").append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Extracts room data.
   */
  private String extractRoom(Room room) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"name\": \"").append(escapeJson(room.getName())).append("\",\n");
    json.append("      \"area\": ").append(room.getArea()).append(",\n");
    json.append("      \"floorColor\": ").append(room.getFloorColor() != null ? "\"#" + Integer.toHexString(room.getFloorColor()) + "\"" : "null").append(",\n");
    json.append("      \"ceilingColor\": ").append(room.getCeilingColor() != null ? "\"#" + Integer.toHexString(room.getCeilingColor()) + "\"" : "null").append(",\n");
    json.append("      \"floorVisible\": ").append(room.isFloorVisible()).append(",\n");
    json.append("      \"ceilingVisible\": ").append(room.isCeilingVisible()).append(",\n");
    json.append("      \"level\": ").append(room.getLevel() != null ? "\"" + escapeJson(room.getLevel().getName()) + "\"" : "null").append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Extracts furniture data.
   */
  private String extractFurniture(HomePieceOfFurniture piece) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"name\": \"").append(escapeJson(piece.getName())).append("\",\n");
    json.append("      \"description\": \"").append(escapeJson(piece.getDescription())).append("\",\n");
    json.append("      \"x\": ").append(piece.getX()).append(",\n");
    json.append("      \"y\": ").append(piece.getY()).append(",\n");
    json.append("      \"elevation\": ").append(piece.getElevation()).append(",\n");
    json.append("      \"angle\": ").append(piece.getAngle()).append(",\n");
    json.append("      \"width\": ").append(piece.getWidth()).append(",\n");
    json.append("      \"depth\": ").append(piece.getDepth()).append(",\n");
    json.append("      \"height\": ").append(piece.getHeight()).append(",\n");
    json.append("      \"visible\": ").append(piece.isVisible()).append(",\n");
    json.append("      \"movable\": ").append(piece.isMovable()).append(",\n");
    json.append("      \"level\": ").append(piece.getLevel() != null ? "\"" + escapeJson(piece.getLevel().getName()) + "\"" : "null").append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Escapes a string for JSON.
   */
  private String escapeJson(String str) {
    if (str == null) return "";
    return str.replace("\\", "\\\\")
              .replace("\"", "\\\"")
              .replace("\n", "\\n")
              .replace("\r", "\\r")
              .replace("\t", "\\t");
  }
}

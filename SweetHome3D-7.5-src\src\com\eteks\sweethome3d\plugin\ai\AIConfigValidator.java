/*
 * AIConfigValidator.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Validates AI provider configurations.
 * 
 * <AUTHOR> Integration Team
 */
public class AIConfigValidator {
  
  /**
   * Validates an AI provider configuration.
   */
  public ValidationResult validate(AIProviderConfig config) {
    List<String> errors = new ArrayList<>();
    
    if (config == null) {
      errors.add("Configuration cannot be null");
      return new ValidationResult(false, errors);
    }
    
    // Validate base URL
    if (isBlank(config.getBaseUrl())) {
      errors.add("Base URL is required");
    } else if (!isValidUrl(config.getBaseUrl())) {
      errors.add("Base URL must be a valid URL");
    }
    
    // Validate API key (required for most providers)
    if (isBlank(config.getApiKey())) {
      // Check if this is a local provider that might not need an API key
      String baseUrl = config.getBaseUrl();
      if (baseUrl != null && !isLocalUrl(baseUrl)) {
        errors.add("API Key is required for remote providers");
      }
    }
    
    // Validate model selection
    if (isBlank(config.getModel())) {
      errors.add("Model selection is required");
    }
    
    // Validate provider name
    if (isBlank(config.getProviderName())) {
      errors.add("Provider name is required");
    }
    
    return new ValidationResult(errors.isEmpty(), errors);
  }
  
  /**
   * Checks if a string is blank (null, empty, or whitespace only).
   */
  private boolean isBlank(String str) {
    return str == null || str.trim().isEmpty();
  }
  
  /**
   * Validates if a string is a valid URL.
   */
  private boolean isValidUrl(String urlString) {
    try {
      new URL(urlString);
      return true;
    } catch (MalformedURLException e) {
      return false;
    }
  }
  
  /**
   * Checks if a URL appears to be a local/localhost URL.
   */
  private boolean isLocalUrl(String urlString) {
    if (urlString == null) {
      return false;
    }
    
    String lowerUrl = urlString.toLowerCase();
    return lowerUrl.contains("localhost") || 
           lowerUrl.contains("127.0.0.1") || 
           lowerUrl.contains("0.0.0.0") ||
           lowerUrl.matches(".*://192\\.168\\..*") ||
           lowerUrl.matches(".*://10\\..*") ||
           lowerUrl.matches(".*://172\\.(1[6-9]|2[0-9]|3[01])\\..*");
  }
}

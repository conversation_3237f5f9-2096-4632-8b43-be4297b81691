/*
 * AIConfigurationManager.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.UserPreferences;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.prefs.Preferences;

/**
 * Manages AI provider configuration storage and retrieval using Sweet Home 3D's preferences system.
 * Handles secure storage of API keys and provider settings.
 * 
 * <AUTHOR> Integration Team
 */
public class AIConfigurationManager {
  private static final String AI_PROVIDER_NAME = "aiProviderName";
  private static final String AI_BASE_URL = "aiBaseUrl";
  private static final String AI_API_KEY = "aiApiKey";
  private static final String AI_MODEL = "aiModel";
  private static final String AI_CUSTOM_HEADERS = "aiCustomHeaders";
  private static final String AI_TEMPERATURE = "aiTemperature";
  private static final String AI_MAX_TOKENS = "aiMaxTokens";
  private static final String AI_TOP_P = "aiTopP";
  private static final String AI_FREQUENCY_PENALTY = "aiFrequencyPenalty";
  private static final String AI_PRESENCE_PENALTY = "aiPresencePenalty";
  
  private final Preferences preferences;
  private final SecureConfigStorage secureStorage;
  
  /**
   * Creates a new configuration manager.
   */
  public AIConfigurationManager() {
    this.preferences = Preferences.userNodeForPackage(AIConfigurationManager.class);
    this.secureStorage = new SecureConfigStorage();
  }
  
  /**
   * Saves the AI provider configuration.
   */
  public void saveConfiguration(AIProviderConfig config) {
    if (config == null) {
      return;
    }
    
    preferences.put(AI_PROVIDER_NAME, config.getProviderName() != null ? config.getProviderName() : "");
    preferences.put(AI_BASE_URL, config.getBaseUrl() != null ? config.getBaseUrl() : "");
    preferences.put(AI_MODEL, config.getModel() != null ? config.getModel() : "");
    
    // Store API key securely
    if (config.getApiKey() != null && !config.getApiKey().isEmpty()) {
      secureStorage.storeApiKey(config.getApiKey());
    }
    
    // Store custom headers as a simple string format
    if (config.getCustomHeaders() != null && !config.getCustomHeaders().isEmpty()) {
      StringBuilder headersStr = new StringBuilder();
      for (Map.Entry<String, String> entry : config.getCustomHeaders().entrySet()) {
        if (headersStr.length() > 0) {
          headersStr.append(";");
        }
        headersStr.append(entry.getKey()).append("=").append(entry.getValue());
      }
      preferences.put(AI_CUSTOM_HEADERS, headersStr.toString());
    } else {
      preferences.remove(AI_CUSTOM_HEADERS);
    }
    
    // Store model parameters
    AIModelParameters params = config.getModelParams();
    if (params != null) {
      preferences.putDouble(AI_TEMPERATURE, params.getTemperature());
      preferences.putInt(AI_MAX_TOKENS, params.getMaxTokens());
      preferences.putDouble(AI_TOP_P, params.getTopP());
      preferences.putInt(AI_FREQUENCY_PENALTY, params.getFrequencyPenalty());
      preferences.putInt(AI_PRESENCE_PENALTY, params.getPresencePenalty());
    }
  }
  
  /**
   * Loads the AI provider configuration.
   */
  public AIProviderConfig loadConfiguration() {
    String providerName = preferences.get(AI_PROVIDER_NAME, "");
    String baseUrl = preferences.get(AI_BASE_URL, "");
    String model = preferences.get(AI_MODEL, "");
    
    if (baseUrl.isEmpty()) {
      return null; // No configuration saved
    }
    
    AIProviderConfig config = new AIProviderConfig();
    config.setProviderName(providerName);
    config.setBaseUrl(baseUrl);
    config.setModel(model);
    
    // Load API key securely
    String apiKey = secureStorage.retrieveApiKey();
    if (apiKey != null && !apiKey.isEmpty()) {
      config.setApiKey(apiKey);
    }
    
    // Load custom headers
    String headersStr = preferences.get(AI_CUSTOM_HEADERS, "");
    if (!headersStr.isEmpty()) {
      Map<String, String> customHeaders = new HashMap<>();
      String[] headerPairs = headersStr.split(";");
      for (String pair : headerPairs) {
        String[] keyValue = pair.split("=", 2);
        if (keyValue.length == 2) {
          customHeaders.put(keyValue[0], keyValue[1]);
        }
      }
      config.setCustomHeaders(customHeaders);
    }
    
    // Load model parameters
    AIModelParameters params = new AIModelParameters();
    params.setTemperature(preferences.getDouble(AI_TEMPERATURE, 0.7));
    params.setMaxTokens(preferences.getInt(AI_MAX_TOKENS, 2048));
    params.setTopP(preferences.getDouble(AI_TOP_P, 1.0));
    params.setFrequencyPenalty(preferences.getInt(AI_FREQUENCY_PENALTY, 0));
    params.setPresencePenalty(preferences.getInt(AI_PRESENCE_PENALTY, 0));
    config.setModelParams(params);
    
    return config;
  }
  
  /**
   * Returns preset configurations for popular providers.
   */
  public List<AIProviderConfig> getPresetConfigurations() {
    List<AIProviderConfig> presets = new ArrayList<>();
    for (AIProviderPreset preset : AIProviderPreset.values()) {
      if (preset != AIProviderPreset.CUSTOM) {
        presets.add(preset.createConfig());
      }
    }
    return presets;
  }
  
  /**
   * Clears all stored configuration.
   */
  public void clearConfiguration() {
    preferences.remove(AI_PROVIDER_NAME);
    preferences.remove(AI_BASE_URL);
    preferences.remove(AI_MODEL);
    preferences.remove(AI_CUSTOM_HEADERS);
    preferences.remove(AI_TEMPERATURE);
    preferences.remove(AI_MAX_TOKENS);
    preferences.remove(AI_TOP_P);
    preferences.remove(AI_FREQUENCY_PENALTY);
    preferences.remove(AI_PRESENCE_PENALTY);
    
    secureStorage.clearApiKey();
  }
  
  /**
   * Returns whether a configuration is currently saved.
   */
  public boolean hasConfiguration() {
    return !preferences.get(AI_BASE_URL, "").isEmpty();
  }
}

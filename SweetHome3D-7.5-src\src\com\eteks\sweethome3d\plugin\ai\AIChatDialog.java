/*
 * AIChatDialog.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 AI Integration Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.*;
import javax.swing.text.BadLocationException;
import javax.swing.text.Style;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Dialog for displaying AI floor plan analysis results.
 * Provides a chat-like interface for AI interactions.
 * 
 * <AUTHOR> Integration Team
 */
public class AIChatDialog extends JDialog {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final Home home;
  
  // UI Components
  private JTextPane chatArea;
  private JTextField inputField;
  private JButton sendButton;
  private JButton newAnalysisButton;
  private JButton settingsButton;
  private JProgressBar analysisProgressBar;
  private JScrollPane chatScrollPane;
  
  // Styles for chat display
  private Style userStyle;
  private Style aiStyle;
  private Style systemStyle;
  
  /**
   * Creates a new AI chat dialog.
   */
  public AIChatDialog(HomeController homeController, AIIntegration aiIntegration) {
    super(SwingUtilities.getWindowAncestor(homeController.getView()), "AI Floor Plan Analysis", false);
    this.homeController = homeController;
    this.aiIntegration = aiIntegration;
    this.home = homeController.getHome();
    
    initializeComponents();
    setupStyles();
    layoutComponents();
    setupEventHandlers();
    
    setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    setSize(600, 500);
    setLocationRelativeTo(getParent());
    
    // Start initial analysis
    startInitialAnalysis();
  }
  
  /**
   * Initializes the UI components.
   */
  private void initializeComponents() {
    // Chat area
    chatArea = new JTextPane();
    chatArea.setEditable(false);
    chatArea.setBackground(Color.WHITE);
    chatScrollPane = new JScrollPane(chatArea);
    chatScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
    chatScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
    
    // Input field and send button
    inputField = new JTextField();
    sendButton = new JButton("Send");
    
    // Action buttons
    newAnalysisButton = new JButton("New Analysis");
    settingsButton = new JButton("Settings");
    
    // Progress bar
    analysisProgressBar = new JProgressBar();
    analysisProgressBar.setIndeterminate(true);
    analysisProgressBar.setString("Analyzing floor plan...");
    analysisProgressBar.setStringPainted(true);
    analysisProgressBar.setVisible(false);
  }
  
  /**
   * Sets up text styles for the chat area.
   */
  private void setupStyles() {
    StyledDocument doc = chatArea.getStyledDocument();
    
    // User style (right-aligned, blue)
    userStyle = doc.addStyle("user", null);
    StyleConstants.setForeground(userStyle, new Color(0, 100, 200));
    StyleConstants.setBold(userStyle, true);
    StyleConstants.setAlignment(userStyle, StyleConstants.ALIGN_RIGHT);
    
    // AI style (left-aligned, dark green)
    aiStyle = doc.addStyle("ai", null);
    StyleConstants.setForeground(aiStyle, new Color(0, 120, 0));
    StyleConstants.setAlignment(aiStyle, StyleConstants.ALIGN_LEFT);
    
    // System style (centered, gray)
    systemStyle = doc.addStyle("system", null);
    StyleConstants.setForeground(systemStyle, Color.GRAY);
    StyleConstants.setItalic(systemStyle, true);
    StyleConstants.setAlignment(systemStyle, StyleConstants.ALIGN_CENTER);
  }
  
  /**
   * Layouts the components in the dialog.
   */
  private void layoutComponents() {
    setLayout(new BorderLayout());
    
    // Top panel with action buttons
    JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
    topPanel.add(newAnalysisButton);
    topPanel.add(settingsButton);
    add(topPanel, BorderLayout.NORTH);
    
    // Center panel with chat area
    add(chatScrollPane, BorderLayout.CENTER);
    
    // Bottom panel with input and progress
    JPanel bottomPanel = new JPanel(new BorderLayout());
    
    // Input panel
    JPanel inputPanel = new JPanel(new BorderLayout());
    inputPanel.add(inputField, BorderLayout.CENTER);
    inputPanel.add(sendButton, BorderLayout.EAST);
    inputPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
    
    bottomPanel.add(inputPanel, BorderLayout.CENTER);
    bottomPanel.add(analysisProgressBar, BorderLayout.SOUTH);
    
    add(bottomPanel, BorderLayout.SOUTH);
  }
  
  /**
   * Sets up event handlers for the components.
   */
  private void setupEventHandlers() {
    sendButton.addActionListener(this::onSendMessage);
    inputField.addActionListener(this::onSendMessage);
    newAnalysisButton.addActionListener(this::onNewAnalysis);
    settingsButton.addActionListener(this::onSettings);
  }
  
  /**
   * Starts the initial floor plan analysis.
   */
  private void startInitialAnalysis() {
    if (!aiIntegration.isConfigured()) {
      showConfigurationPrompt();
      return;
    }
    
    appendToChat("System", "Starting floor plan analysis...", systemStyle);
    
    analysisProgressBar.setVisible(true);
    sendButton.setEnabled(false);
    newAnalysisButton.setEnabled(false);
    
    aiIntegration.analyzeHome(home)
        .thenAccept(this::displayAnalysis)
        .exceptionally(this::handleAnalysisError);
  }
  
  /**
   * Shows a configuration prompt if AI is not configured.
   */
  private void showConfigurationPrompt() {
    appendToChat("System", "AI is not configured. Please click Settings to configure your AI provider.", systemStyle);
    sendButton.setEnabled(false);
    newAnalysisButton.setEnabled(false);
  }
  
  /**
   * Displays the AI analysis result.
   */
  private void displayAnalysis(String analysis) {
    SwingUtilities.invokeLater(() -> {
      appendToChat("AI Assistant", analysis, aiStyle);
      analysisProgressBar.setVisible(false);
      sendButton.setEnabled(true);
      newAnalysisButton.setEnabled(true);
    });
  }
  
  /**
   * Handles analysis errors.
   */
  private Void handleAnalysisError(Throwable error) {
    SwingUtilities.invokeLater(() -> {
      new AIErrorHandler().handleAnalysisError(error, this);
      appendToChat("System", "Analysis failed. Please check your AI settings and try again.", systemStyle);
      analysisProgressBar.setVisible(false);
      sendButton.setEnabled(true);
      newAnalysisButton.setEnabled(true);
    });
    return null;
  }
  
  /**
   * Appends a message to the chat area.
   */
  private void appendToChat(String sender, String message, Style style) {
    try {
      StyledDocument doc = chatArea.getStyledDocument();
      
      // Add timestamp and sender
      String timestamp = new SimpleDateFormat("HH:mm").format(new Date());
      String header = String.format("[%s] %s:\n", timestamp, sender);
      
      doc.insertString(doc.getLength(), header, style);
      doc.insertString(doc.getLength(), message + "\n\n", style);
      
      // Scroll to bottom
      chatArea.setCaretPosition(doc.getLength());
      
    } catch (BadLocationException e) {
      // Handle error silently
    }
  }
  
  /**
   * Handles send message action.
   */
  private void onSendMessage(ActionEvent e) {
    String message = inputField.getText().trim();
    if (message.isEmpty() || !aiIntegration.isConfigured()) {
      return;
    }
    
    // Display user message
    appendToChat("You", message, userStyle);
    inputField.setText("");
    
    // Send to AI (for follow-up questions)
    sendButton.setEnabled(false);
    analysisProgressBar.setString("Processing question...");
    analysisProgressBar.setVisible(true);
    
    aiIntegration.askQuestion(home, message)
        .thenAccept(this::displayAnalysis)
        .exceptionally(this::handleAnalysisError);
  }
  
  /**
   * Handles new analysis action.
   */
  private void onNewAnalysis(ActionEvent e) {
    // Clear chat area
    chatArea.setText("");
    
    // Start new analysis
    startInitialAnalysis();
  }
  
  /**
   * Handles settings action.
   */
  private void onSettings(ActionEvent e) {
    AISettingsDialog settingsDialog = new AISettingsDialog(homeController, aiIntegration);
    settingsDialog.setVisible(true);
    
    if (settingsDialog.isConfigurationSaved()) {
      aiIntegration.reconfigure();
      appendToChat("System", "AI configuration updated.", systemStyle);
      
      // Enable controls if now configured
      if (aiIntegration.isConfigured()) {
        sendButton.setEnabled(true);
        newAnalysisButton.setEnabled(true);
      }
    }
  }
}
